// //*-- s1 --*/
.s1
  $root: &
  width: 100%
  position: relative
  // padding: 80px 0
  background-color: #ffffff

  &__grid
    display: grid
    grid-template-columns: repeat(2, 1fr)
    gap: 20px
    margin: 0 auto
    
    @media (max-width: $bk-tb)
      grid-template-columns: 1fr
      gap: 15px

  &__card
    position: relative

    &::after
      content: ''
      position: absolute
      top: -40px
      left: 0
      width: 100%
      height: 100%
      background-image: url($path + 's1_shadow.png')
      background-size: 100% auto
      background-repeat: no-repeat
      background-position: center
      z-index: 1
      opacity: 0.6
      pointer-events: none

    img
      width: 100%
      height: auto
      display: block
      transition: transform 0.3s ease
      position: relative
      z-index: 2

      &:hover
        transform: translateY(-5px)

  &__more
    position: relative
    margin-top: 95px
    display: flex
    height: 160px
    align-items: center
    justify-content: space-between
    border-radius: 20px
    padding: 43px 40px
    background-color: #ffffff
    z-index: 2
    @media (max-width: $bk-tb)
      flex-direction: column
      text-align: center
      gap: 20px

    &-content
      flex: 1
      display: flex
      align-items: center
      gap: 50px
      position: relative
      z-index: 3
      @media (max-width: $bk-tb)
        flex-direction: column
        align-items: flex-start
        gap: 15px

    &-title
      font-size: $fontsize-40
      font-weight: $fontweight-light
      color: #333333
      flex-shrink: 0

    &-tags
      display: grid
      grid-template-columns: repeat(3, 1fr)
      grid-template-rows: repeat(2, 1fr)
      gap: 10px
      text-align: center
      @media (max-width: $bk-tb)
        max-width: 100%

    &-tag
      width: 150px

    &-btn
      background: white
      color: #333333
      border: 1px solid #7a7fff
      padding: 4px 16px
      border-radius: 25px
      font-size: $fontsize-20
      cursor: pointer
      transition: all 0.3s ease
      display: inline-flex
      align-items: center
      justify-content: center
      text-align: center

      &:hover
        background: #6c5ce7
        color: white
        border-color: #6c5ce7
        transform: translate(2px, 2px)

    &-action
      position: relative
      z-index: 3

    // 探索更多按鈕的特殊樣式
    &-action &-btn
      padding: 15px 30px
      border-radius: 37px
      border: 1px solid #7a7fff
      width: 220px
      height: 74px
      // min-width: 140px
      gap: 8px

  // 移除 more 的 ::before，改到 s1 層級

  // s1_more_bg 背景圖片
  &::before
    content: ''
    position: absolute
    top: calc(100% - 80px)
    left: 50%
    width: 1112px
    height: 439px
    background-image: url($path + 's1_more_bg.png')
    background-size: 60% auto
    background-repeat: no-repeat
    background-position: center
    transform: translate(-50%, -50%)
    pointer-events: none
    z-index: 1

  &::after
    content: ''
    position: absolute
    top: 93%
    left: 77%
    width: 300px
    height: 140px
    pointer-events: none
    background-image: url($path + 's1_people.png')
    background-size: 100% auto
    background-repeat: no-repeat
    background-position: top
    transform: translate(-50%, -50%)
    z-index: 4
