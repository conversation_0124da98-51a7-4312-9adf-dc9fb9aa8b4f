@charset "UTF-8";
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, ar, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
}

@font-face {
  font-family: "Noto Sans TC";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(../vendor/fonts/NotoSansTC-Regular.ttf) format("truetype");
}
@font-face {
  font-family: "Noto Sans TC";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(../vendor/fonts/NotoSansTC-Medium.ttf) format("truetype");
}
@font-face {
  font-family: "Noto Sans TC";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(../vendor/fonts/NotoSansTC-Bold.ttf) format("truetype");
}
.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

@-webkit-keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
.fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
}

@-webkit-keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale(0);
            transform: scale(0);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale(0);
            transform: scale(0);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
.zoomIn {
  -webkit-animation-name: zoomIn;
  animation-name: zoomIn;
}

@-webkit-keyframes heartbeat {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}

@keyframes heartbeat {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
.heartbeat {
  -webkit-animation-name: heartbeat;
  animation-name: heartbeat;
}

@-webkit-keyframes flash {
  0% {
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes flash {
  0% {
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.flash {
  -webkit-animation-name: flash;
  animation-name: flash;
}

body {
  font-family: "Noto Sans TC", "微軟正黑體", sans-serif;
  color: #000000;
  margin: 0 auto;
  font-weight: 400;
  line-height: 1.2;
  position: relative;
  overflow-x: hidden;
}

select, button, textarea, input {
  font-family: "Noto Sans TC", "微軟正黑體", sans-serif;
  outline: none;
}

* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: none;
}

table {
  border-collapse: collapse;
}

a, a:focus {
  cursor: pointer;
  text-decoration: none;
  -webkit-transition: color 300ms;
  transition: color 300ms;
}

a:active {
  outline: none;
}

img {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  max-width: 100%;
}

.container {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 768px) {
  .container {
    width: 750px;
    padding-right: 0px;
    padding-left: 0px;
  }
}
@media (min-width: 992px) {
  .container {
    width: 850px;
  }
}
@media (min-width: 1200px) {
  .container {
    width: 1050px;
  }
}
@media (min-width: 1400px) {
  .container {
    width: 1350px;
  }
}

.header {
  background: #ffffff;
  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1030;
}

.navbar {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 75px;
}
.navbar__logo {
  display: block;
  margin: 0 auto;
  width: 192px;
}
@media (max-width: 991px) {
  .navbar__logo {
    width: 220px;
    position: relative;
    z-index: 3;
  }
}
@media (max-width: 480px) {
  .navbar__logo {
    width: 200px;
  }
}

.footer {
  position: relative;
  margin-top: auto;
  background: linear-gradient(93deg, #4919bd 19%, #4f6dd4 88%);
  text-align: center;
  line-height: 1.6;
  z-index: 1;
}
.footer__wrap {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 0.875em;
  padding: 6px 72px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 auto;
}
.footer ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.footer li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-left: 1px solid #ffffff;
  padding-left: 12px;
  margin-left: 12px;
  line-height: 1em;
}
.footer li:first-child {
  border-left: none;
  padding-left: 0;
  margin-left: 0;
}
.footer a {
  color: #ffffff;
  font-family: Montserrat, NotoSansTC, 微軟正黑體, "Microsoft JhengHei", "Heiti TC", 黑體, sans-serif;
}

.kv {
  height: 980px;
  position: relative;
  text-align: center;
  background-image: url("../images/kv_bottom.png"), url("../images/kv_people.png"), url("../images/kv_bg.jpg");
  background-size: 100% auto, 1240px auto, cover;
  background-position: bottom, calc(50% - 90px) 98%, center;
  background-repeat: no-repeat, no-repeat, no-repeat;
}
@media (max-width: 480px) {
  .kv {
    padding-top: 50px;
  }
}
.kv::after {
  content: "";
  position: absolute;
  top: 317px;
  left: 226px;
  width: 1240px;
  height: 640px;
  background-image: url("../images/kv_people_light.png");
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: center;
  pointer-events: none;
  -webkit-animation: flash 1.5s ease infinite;
          animation: flash 1.5s ease infinite;
}
.kv__title {
  display: block;
  width: 719px;
  height: auto;
  position: relative;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-45%, -70%);
          transform: translate(-45%, -70%);
}
@media (max-width: 767px) {
  .kv__title {
    margin-top: 57px;
  }
}
.kv__title--desktop {
  display: block;
}
@media (max-width: 767px) {
  .kv__title--desktop {
    display: none;
  }
}
.kv__title--mobile {
  display: none;
}
@media (max-width: 767px) {
  .kv__title--mobile {
    display: block;
  }
}
.kv__subtitle {
  position: relative;
  color: #3c7d5a;
  font-size: 1.875em;
  font-weight: 700;
  background-color: #ffffff;
  padding: 15px 10px 15px 30px;
  border-radius: 30px;
  display: inline-block;
  top: 345px;
  -webkit-box-shadow: 0 -9px 10px rgba(0, 0, 0, 0.1);
          box-shadow: 0 -9px 10px rgba(0, 0, 0, 0.1);
}
.kv__subtitle-text {
  padding-right: 20px;
}
.kv__subtitle-text--highlight {
  padding: 2px 30px;
  color: #ffeeb9;
  background-color: #3c7d5a;
  border-radius: 30px;
}

.s1 {
  width: 100%;
  position: relative;
  background-color: #ffffff;
}
.s1__grid {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 1fr 20px 1fr;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin: 0 auto;
}
@media (max-width: 991px) {
  .s1__grid {
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    gap: 15px;
  }
}
.s1__card {
  position: relative;
}
.s1__card::after {
  content: "";
  position: absolute;
  top: -40px;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/s1_shadow.png");
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 1;
  opacity: 0.6;
  pointer-events: none;
}
.s1__card img {
  width: 100%;
  height: auto;
  display: block;
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  position: relative;
  z-index: 2;
}
.s1__card img:hover {
  -webkit-transform: translateY(-5px);
          transform: translateY(-5px);
}
.s1__more {
  position: relative;
  margin-top: 95px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 160px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  border-radius: 20px;
  padding: 43px 40px;
  background-color: #ffffff;
  z-index: 2;
}
@media (max-width: 991px) {
  .s1__more {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    text-align: center;
    gap: 20px;
  }
}
.s1__more-content {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 50px;
  position: relative;
  z-index: 3;
}
@media (max-width: 991px) {
  .s1__more-content {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    gap: 15px;
  }
}
.s1__more-title {
  font-size: 2.5em;
  font-weight: 300;
  color: #333333;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
.s1__more-tags {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 1fr 10px 1fr 10px 1fr;
  grid-template-columns: repeat(3, 1fr);
  -ms-grid-rows: 1fr 10px 1fr;
  grid-template-rows: repeat(2, 1fr);
  gap: 10px;
  text-align: center;
}
.s1__more-tags > *:nth-child(1) {
  -ms-grid-row: 1;
  -ms-grid-column: 1;
}
.s1__more-tags > *:nth-child(2) {
  -ms-grid-row: 1;
  -ms-grid-column: 3;
}
.s1__more-tags > *:nth-child(3) {
  -ms-grid-row: 1;
  -ms-grid-column: 5;
}
.s1__more-tags > *:nth-child(4) {
  -ms-grid-row: 3;
  -ms-grid-column: 1;
}
.s1__more-tags > *:nth-child(5) {
  -ms-grid-row: 3;
  -ms-grid-column: 3;
}
.s1__more-tags > *:nth-child(6) {
  -ms-grid-row: 3;
  -ms-grid-column: 5;
}
@media (max-width: 991px) {
  .s1__more-tags {
    max-width: 100%;
  }
}
.s1__more-tag {
  width: 150px;
}
.s1__more-btn {
  background: white;
  color: #333333;
  border: 1px solid #7a7fff;
  padding: 4px 16px;
  border-radius: 25px;
  font-size: 1.25em;
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
}
.s1__more-btn:hover {
  background: #6c5ce7;
  color: white;
  border-color: #6c5ce7;
  -webkit-transform: translate(2px, 2px);
          transform: translate(2px, 2px);
}
.s1__more-action {
  position: relative;
  z-index: 3;
}
.s1__more-action .s1__more-btn {
  padding: 15px 30px;
  border-radius: 37px;
  border: 1px solid #7a7fff;
  width: 220px;
  height: 74px;
  gap: 8px;
}
.s1::before {
  content: "";
  position: absolute;
  top: calc(100% - 80px);
  left: 50%;
  width: 1112px;
  height: 439px;
  background-image: url("../images/s1_more_bg.png");
  background-size: 60% auto;
  background-repeat: no-repeat;
  background-position: center;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 1;
}
.s1::after {
  content: "";
  position: absolute;
  top: 93%;
  left: 77%;
  width: 300px;
  height: 140px;
  pointer-events: none;
  background-image: url("../images/s1_people.png");
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: top;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  z-index: 4;
}

.s2 {
  padding-top: 126px;
  padding-bottom: 65px;
}
.s2__item {
  width: 1050px;
  height: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 0 auto;
}

.s3 {
  width: 100%;
  height: 320px;
  position: relative;
  background: #fff8e1;
}
.s3__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 55px;
}
@media (max-width: 991px) {
  .s3__content {
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    text-align: center;
  }
}
.s3__title {
  font-size: 2.5em;
  font-weight: 300;
  color: #333333;
  text-align: center;
  line-height: 1.2;
  min-width: 100px;
}
@media (max-width: 991px) {
  .s3__title {
    font-size: 2em;
  }
}
.s3__illustration {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 133px;
  bottom: 17px;
  z-index: 1;
}
.s3__illustration img {
  min-width: 304px;
  height: auto;
  display: block;
}
.s3__services {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-right: auto;
  margin-left: -10px;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
.s3__services::after {
  content: "";
  position: absolute;
  background-image: url("../images/s3_shadow.png");
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: center;
  width: 790px;
  height: 28px;
  top: 52%;
  left: 40%;
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  z-index: 0;
}
.s3__service {
  border-radius: 25px;
  padding: 50px 0px 50px 50px;
  -webkit-transition: -webkit-transform 0.3s ease, -webkit-box-shadow 0.3s ease;
  transition: -webkit-transform 0.3s ease, -webkit-box-shadow 0.3s ease;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  transition: transform 0.3s ease, box-shadow 0.3s ease, -webkit-transform 0.3s ease, -webkit-box-shadow 0.3s ease;
  cursor: pointer;
  text-align: left;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  gap: 10px;
  margin-right: auto;
  max-width: 470px;
}
.s3__service:hover {
  -webkit-transform: translateY(-3px);
          transform: translateY(-3px);
  -webkit-box-shadow: 0 6px 12px rgba(230, 190, 60, 0.15);
          box-shadow: 0 6px 12px rgba(230, 190, 60, 0.15);
}
.s3__service-label {
  font-size: 1.625em;
  color: #e85d3f;
  white-space: nowrap;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  min-width: 120px;
  margin: auto;
}
.s3__service-desc {
  font-size: 1.125em;
  color: #333333;
  line-height: 1.5;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  word-wrap: break-word;
}

.title {
  text-align: center;
  font-size: 2.5em;
  font-weight: 300;
  color: #333333;
  margin-top: -50px;
  margin-bottom: 56px;
}