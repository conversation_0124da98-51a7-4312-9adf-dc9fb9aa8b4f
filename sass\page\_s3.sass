// //*-- s3 --*/
.s3
  $root: &
  width: 100%
  height: 320px
  position: relative
  background: #fff8e1
  &__content
    display: flex
    align-items: center
    margin: 0 55px
    @media (max-width: $bk-tb)
      grid-template-columns: 1fr
      text-align: center

  &__title
    font-size: $fontsize-40
    font-weight: $fontweight-light
    color: #333333
    text-align: center
    line-height: 1.2
    min-width: 100px
    @media (max-width: $bk-tb)
      font-size: $fontsize-32


  &__illustration
    position: relative
    display: flex
    padding-left: 133px
    // justify-content: center
    // align-items: center
    bottom: 17px
    z-index: 1

    img
      min-width: 304px
      height: auto
      display: block
      
  &__services
    display: flex
    flex-direction: column
    align-items: center
    margin-right: auto
    // padding-left: 43px
    margin-left: -10px
    flex: 1
    &::after
      content: ''
      position: absolute
      background-image: url($path + 's3_shadow.png')
      background-size: 100% auto
      background-repeat: no-repeat
      background-position: center
      width: 790px
      height: 28px
      top: 52%
      left: 40%
      transition: transform 0.3s ease
      z-index: 0

  &__service
    border-radius: 25px
    padding: 50px 0px 50px 50px
    // box-shadow: 0 8px 0px rgba(0, 0, 0, 0.1)
    transition: transform 0.3s ease, box-shadow 0.3s ease
    cursor: pointer
    text-align: left
    display: flex
    align-items: flex-start
    gap: 10px
    margin-right: auto
    max-width: 470px
    // margin-bottom: 20px
    // flex: 1

    &:hover
      transform: translateY(-3px)
      box-shadow: 0 6px 12px rgba(#e6be3c, 0.15)

    &-label
      font-size: $fontsize-26
      // font-weight: $fontweight-bold
      color: #e85d3f
      white-space: nowrap
      flex-shrink: 0
      min-width: 120px
      margin: auto

    &-desc
      font-size: $fontsize-18
      color: #333333
      line-height: 1.5
      flex: 1
      word-wrap: break-word
