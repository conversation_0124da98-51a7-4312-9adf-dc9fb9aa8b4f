// /*-- mixins --*/
//icon
// @mixin icon($img: icon,$p: 0px 0px,$size: 184px 80px)
//   background-size: $size
//   background-repeat: no-repeat
//   background-image: url($path+$img+".png")
//   background-position: $p

//hover-second
@mixin hover-second($s:.3s)
  transition-timing-function: ease
  transition-duration: $s
  
@mixin hover-second-o($s:.3s)
  transition: opacity $s ease

// @mixin clear($v:both)
//   content: ""
//   display: block
//   clear: $v
//   height: 0
//   visibility: hidden